import { LayoutGroup, motion } from "framer-motion"

import RotatingText from "@/components/ui/rotating-text"

const AboutMe = () => {
  const words = ["Full Stack", "Django", "React", "Nerdy"]

  const transition = {
    type: "spring",
    damping: 30,
    stiffness: 400
  }

  return (
    <section
      id="about_me"
      className="section flex flex-col items-center justify-center gap-4 !pt-0"
    >
      <div>
        <span className="text-2xl">
          Hello, I am Jaimish Trivedi
        </span>
      </div>
      <div>
        <LayoutGroup>
          <motion.p
            className="flex items-center gap-4 text-lg text-white"
            layout
          >
            <RotatingText
              texts={words}
              mainClassName="px-2 sm:px-2 md:px-3 text-8xl bg-primary text-black overflow-hidden py-0.5 sm:py-1 md:py-2 justify-center rounded-xl"
              staggerFrom={"last"}
              initial={{ y: "100%" }}
              animate={{ y: 0 }}
              exit={{ y: "-120%" }}
              staggerDuration={0.025}
              splitLevelClassName="pb-1"
              transition={transition}
              rotationInterval={2000}
            />
            <motion.span
              className="pt-0.5 text-8xl sm:pt-1 md:pt-2"
              layout
              transition={transition}
            >
              {" "}
              Developer
            </motion.span>
          </motion.p>
        </LayoutGroup>
      </div>
    </section>
  )
}

export default AboutMe
